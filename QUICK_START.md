# LIO-SAM Docker 快速启动指南

## 🚀 5分钟快速启动

### 步骤1: 网络配置
```bash
# 配置网络连接激光雷达（需要sudo权限）
sudo ./scripts/setup_network.sh
```

### 步骤2: 构建Docker环境
```bash
# 构建Docker镜像（首次运行需要10-15分钟）
./scripts/start_lio_sam.sh build
```

### 步骤3: 启动系统
```bash
# 启动Docker容器
./scripts/start_lio_sam.sh start

# 检查系统状态
./scripts/start_lio_sam.sh status
```

### 步骤4: 运行LIO-SAM
```bash
# 在新终端中启动激光雷达驱动
./scripts/start_lio_sam.sh lidar

# 在另一个新终端中启动SLAM系统
./scripts/start_lio_sam.sh slam

# 启动可视化（可选）
./scripts/start_lio_sam.sh rviz
```

## 📋 检查清单

在开始之前，请确保：

- [ ] 激光雷达已连接并开机
- [ ] 主机可以ping通***************
- [ ] Docker和Docker Compose已安装
- [ ] 有足够的磁盘空间（至少5GB）

## 🔧 常用命令

```bash
# 查看容器状态
./scripts/start_lio_sam.sh status

# 查看日志
./scripts/start_lio_sam.sh logs

# 进入容器调试
./scripts/start_lio_sam.sh shell

# 停止系统
./scripts/start_lio_sam.sh stop

# 清理资源
./scripts/start_lio_sam.sh clean
```

## ⚠️ 故障排除

### 网络问题
```bash
# 测试激光雷达连接
ping ***************

# 重新配置网络
sudo ./scripts/setup_network.sh
```

### Docker问题
```bash
# 检查Docker状态
sudo systemctl status docker

# 重启Docker服务
sudo systemctl restart docker
```

### 权限问题
```bash
# 添加用户到docker组
sudo usermod -aG docker $USER
# 注销并重新登录

# 允许X11转发
xhost +local:docker
```

## 📊 系统架构

```
激光雷达 (***************) ←→ Docker容器 (ROS1) ←→ 主机 (ROS2)
```

## 🎯 预期结果

成功启动后，您应该看到：
1. 激光雷达数据在RViz中显示
2. LIO-SAM生成实时地图
3. 里程计信息正常输出

## 📞 获取帮助

如果遇到问题：
1. 查看 `README.md` 获取详细文档
2. 运行 `./scripts/start_lio_sam.sh status` 检查系统状态
3. 查看容器日志：`./scripts/start_lio_sam.sh logs`
