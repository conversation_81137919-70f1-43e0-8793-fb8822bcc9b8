# Docker Compose配置文件
# 用于运行ROS1 Noetic LIO-SAM SLAM系统
version: '3.8'

services:
  # LIO-SAM主服务
  lio-sam:
    build:
      context: .
      dockerfile: Dockerfile
    image: lio-sam:noetic
    container_name: lio-sam-container
    
    # 网络配置 - 使用host网络模式以便直接访问激光雷达
    network_mode: host
    
    # 特权模式，允许访问硬件设备
    privileged: true
    
    # 环境变量
    environment:
      - DISPLAY=${DISPLAY}
      - ROS_MASTER_URI=http://localhost:11311
      - ROS_HOSTNAME=localhost
      - QT_X11_NO_MITSHM=1
    
    # 卷挂载
    volumes:
      # X11转发用于GUI应用（如RViz）
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - /dev/shm:/dev/shm
      # 挂载源代码目录（开发模式）
      - ./src:/catkin_ws/src:rw
      # 挂载配置文件
      - ./config:/catkin_ws/config:rw
      # 保存日志和数据
      - ./logs:/catkin_ws/logs:rw
      - ./data:/catkin_ws/data:rw
    
    # 设备访问
    devices:
      - /dev/dri:/dev/dri  # GPU访问（如果需要）
    
    # 工作目录
    working_dir: /catkin_ws
    
    # 重启策略
    restart: unless-stopped
    
    # 健康检查
    healthcheck:
      test: ["CMD", "ping", "-c", "1", "***************"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 标签
    labels:
      - "project=lio-sam"
      - "environment=development"

  # 可选：RViz可视化服务（独立容器）
  rviz:
    image: lio-sam:noetic
    container_name: lio-sam-rviz
    network_mode: host
    
    environment:
      - DISPLAY=${DISPLAY}
      - ROS_MASTER_URI=http://localhost:11311
      - ROS_HOSTNAME=localhost
      - QT_X11_NO_MITSHM=1
    
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
      - ./src:/catkin_ws/src:ro
    
    devices:
      - /dev/dri:/dev/dri
    
    working_dir: /catkin_ws
    
    # 默认不启动，需要手动启动
    profiles:
      - visualization
    
    command: >
      bash -c "source /opt/ros/noetic/setup.bash &&
               source devel/setup.bash &&
               rosrun rviz rviz -d src/ws_30pcd_et3_ros/rviz/points_imu.rviz"
    
    depends_on:
      - lio-sam

# 网络配置（如果不使用host模式）
networks:
  default:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 卷定义
volumes:
  lio_sam_logs:
    driver: local
  lio_sam_data:
    driver: local
