# ws_30pcd_et3纯固态面阵雷达驱动包

**前提环境**

本代码基于ROS1，有关ROS1的安装教程参考[ROS官网](https://www.ros.org)，
或者[ROS WIKI](https://wiki.ros.org/cn/)

**创建工作空间和编译代码**
```
mkdir -p ~/catkin_ws/src
cd ~/catkin_ws/src
catkin_init_workspace 

将ws_30pcd_et3这个包放到catkin_ws/src目录，然后编译
cd ~/catkin_ws/
catkin_make

设置环境变量：
echo "source ~/catkin_ws/devel/setup.bash" >> ~/.bashrc
```

**连接设备**
```
设置电脑IP地址为***************（雷达的IP默认为***************，同网段即可），网关*************
```

**测试连接**
```
ping ***************
```

**启动代码**
```
启动单个雷达：
roslaunch ws_30pcd_et3 scan_frame.launch

如果同一台电脑启动两个雷达：
roslaunch ws_30pcd_et3 scan_frame_with_2_lidars.launch

如果启动代码时找不到ROS节点，可以输入以下指令再启动：
$cd ~/catkin_ws/
$source devel/setup.bash
```

**消息调用**
```
获取雷达和IMU的数据:
rostopic pub -1 /ws_state_num std_msgs/Int32 "data: 1"
获取雷达的SN码:
rostopic pub -1 /ws_state_num std_msgs/Int32 "data: 2" 
进入低功耗模式:
rostopic pub -1 /ws_state_num std_msgs/Int32 "data: 3"
恢复正常工作模式:
rostopic pub -1 /ws_state_num std_msgs/Int32 "data: 4"
```

**代码说明**
```
雷达frame_id为ws_scan，topic为/ws_points_raw
imu的frame_id为ws_imu，topic为/ws_imu_raw

calculate_imu节点为imu六轴原始数据解算为roll和pitch的数据
```

**时间同步**
```
库文件在/ws_30pcd_et3/data目录

先安装ptpd，在自己笔记本
sudo apt install libpcap-dev
cd ptpd
make

设置笔记本端为PTP协议的Master，做为时间同步源（每次笔记本开机启动一次即可）
cd ~/ptpd/src
sudo ./ptpd2 -M -i eno1 #这个eno1根据自己笔记本设置，ifconfig查看***************的配置
配置完成即可同步笔记本时间和雷达的时间
```

**前端界面**
```
推荐使用谷歌浏览器查看

网页端输入：
***************

登陆用户名密码：
wittyrobotics
admin
```

