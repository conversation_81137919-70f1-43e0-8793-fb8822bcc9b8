<!-- -->
<launch>

    <node pkg="tf" type="static_transform_publisher" name="link_broadcaster" args="0 0 0 0 0 0 1 ws_scan ws_scan2 50" /> 

    <node pkg="ws_30pcd_et3" type="scan_frame" name="scan_frame" output="screen" respawn="true"> <!--雷达节点-->
      <param name="ip_address" type="string" value="***************" /> <!--服务器IP地址-->
      <param name="frame_id" type="string" value="ws_scan" /> <!--雷达名字-->
      <param name="scan_topic" type="string" value="ws_points_raw" /> <!--雷达话题-->
      <param name="imu_frame_id" type="string" value="ws_imu" /> <!--imu名字-->
      <param name="imu_topic" type="string" value="ws_imu_raw" /> <!--imu话题-->
    </node> 

    <node pkg="ws_30pcd_et3" type="scan_frame" name="scan_frame2" output="screen" respawn="true"> <!--雷达节点-->
      <param name="ip_address" type="string" value="***************" /> <!--服务器IP地址-->
      <param name="frame_id" type="string" value="ws_scan2" /> <!--雷达名字-->
      <param name="scan_topic" type="string" value="ws_points_raw2" /> <!--雷达话题-->	
      <param name="imu_frame_id" type="string" value="ws_imu2" /> <!--imu名字-->
      <param name="imu_topic" type="string" value="ws_imu_raw2" /> <!--imu话题-->
    </node> 

    <node name="rviz" pkg="rviz" type="rviz" args="-d $(find ws_30pcd_et3)/rviz/points_imu_with_2_lidars.rviz"/>

</launch>
