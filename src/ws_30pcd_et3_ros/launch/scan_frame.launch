<!-- -->
<launch>

    <node pkg="ws_30pcd_et3" type="scan_frame" respawn="true" name="scan_frame" output="screen"> <!--雷达节点-->
      <param name="ip_address" type="string" value="***************" /> <!--服务器IP地址-->
      <param name="scan_frame_id" type="string" value="ws_scan" /> <!--雷达名字-->
      <param name="scan_topic" type="string" value="ws_points_raw" /> <!--雷达话题-->
      <param name="imu_frame_id" type="string" value="ws_imu" /> <!--imu名字-->
      <param name="imu_topic" type="string" value="ws_imu_raw" /> <!--imu话题-->
    </node> 

    <node name="rviz" pkg="rviz" type="rviz" args="-d $(find ws_30pcd_et3)/rviz/points_imu.rviz"/>

</launch>
