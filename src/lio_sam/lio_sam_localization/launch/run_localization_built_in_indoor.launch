<launch>

    <arg name="project" default="lio_sam_localization"/>
    
    <!-- Parameters -->
    <rosparam file="$(find lio_sam_localization)/config/localization_built_in_indoor.yaml" command="load" />

    <!--- LOAM -->
    <include file="$(find lio_sam_localization)/launch/include/module_loam.launch" />

    <!--- Run Rviz-->
    <include file="$(find lio_sam_localization)/launch/include/module_rviz.launch" />

</launch>
