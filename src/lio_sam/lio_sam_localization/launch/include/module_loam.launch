<launch>

    <arg name="project" default="lio_sam_localization"/>
    
    <env name="ROSCONSOLE_CONFIG_FILE" value="$(find lio_sam_localization)/launch/include/rosconsole/rosconsole_info.conf"/>
    
    <node pkg="$(arg project)" type="$(arg project)_imuPreintegration"   name="$(arg project)_imuPreintegration"    output="screen" 	respawn="true"/>
    <node pkg="$(arg project)" type="$(arg project)_imageProjection"     name="$(arg project)_imageProjection"      output="screen"     respawn="true"/>
    <node pkg="$(arg project)" type="$(arg project)_mapOptmization"      name="$(arg project)_mapOptmization"       output="screen"     respawn="true"/>
    
</launch>