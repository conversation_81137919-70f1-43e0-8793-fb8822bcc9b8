#!/bin/bash
# 网络配置脚本
# 用于配置主机网络以连接激光雷达设备

set -e

echo "=== LIO-SAM Docker网络配置脚本 ==="
echo

# 检查是否以root权限运行
if [[ $EUID -ne 0 ]]; then
   echo "此脚本需要root权限运行"
   echo "请使用: sudo $0"
   exit 1
fi

# 激光雷达IP配置
LIDAR_IP="***************"
HOST_IP="***************"
SUBNET="*************/24"
INTERFACE=""

echo "1. 检测网络接口..."

# 自动检测可用的网络接口
INTERFACES=$(ip link show | grep -E "^[0-9]+:" | grep -v "lo:" | awk -F': ' '{print $2}' | cut -d'@' -f1)

echo "可用的网络接口:"
for iface in $INTERFACES; do
    echo "  - $iface"
done

echo
read -p "请选择要配置的网络接口 (例如: eth0, enp0s3): " INTERFACE

if [[ -z "$INTERFACE" ]]; then
    echo "错误: 必须指定网络接口"
    exit 1
fi

# 检查接口是否存在
if ! ip link show "$INTERFACE" > /dev/null 2>&1; then
    echo "错误: 网络接口 $INTERFACE 不存在"
    exit 1
fi

echo
echo "2. 配置网络接口 $INTERFACE..."

# 配置静态IP
ip addr flush dev "$INTERFACE"
ip addr add "$HOST_IP/24" dev "$INTERFACE"
ip link set "$INTERFACE" up

echo "✓ 已配置 $INTERFACE 的IP地址为 $HOST_IP"

echo
echo "3. 测试与激光雷达的连接..."

# 测试连接
if ping -c 3 -W 5 "$LIDAR_IP" > /dev/null 2>&1; then
    echo "✓ 成功连接到激光雷达 ($LIDAR_IP)"
else
    echo "⚠ 警告: 无法连接到激光雷达 ($LIDAR_IP)"
    echo "请检查:"
    echo "  1. 激光雷达是否已开机"
    echo "  2. 网线连接是否正常"
    echo "  3. 激光雷达IP是否为 $LIDAR_IP"
fi

echo
echo "4. 配置防火墙规则..."

# 允许与激光雷达子网的通信
if command -v ufw > /dev/null 2>&1; then
    ufw allow from "$SUBNET"
    echo "✓ 已配置UFW防火墙规则"
elif command -v iptables > /dev/null 2>&1; then
    iptables -I INPUT -s "$SUBNET" -j ACCEPT
    iptables -I OUTPUT -d "$SUBNET" -j ACCEPT
    echo "✓ 已配置iptables防火墙规则"
fi

echo
echo "=== 网络配置完成 ==="
echo "主机IP: $HOST_IP"
echo "激光雷达IP: $LIDAR_IP"
echo "网络接口: $INTERFACE"
echo
echo "现在可以启动Docker容器:"
echo "  docker-compose up -d"
echo
