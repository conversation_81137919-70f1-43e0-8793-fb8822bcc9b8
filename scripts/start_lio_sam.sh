#!/bin/bash
# LIO-SAM Docker启动脚本
# 用于快速启动和管理LIO-SAM SLAM系统

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CONTAINER_NAME="lio-sam-container"
IMAGE_NAME="lio-sam:noetic"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "LIO-SAM Docker管理脚本"
    echo
    echo "用法: $0 [选项] [命令]"
    echo
    echo "命令:"
    echo "  build       构建Docker镜像"
    echo "  start       启动LIO-SAM容器"
    echo "  stop        停止LIO-SAM容器"
    echo "  restart     重启LIO-SAM容器"
    echo "  logs        查看容器日志"
    echo "  shell       进入容器shell"
    echo "  rviz        启动RViz可视化"
    echo "  lidar       启动激光雷达驱动"
    echo "  slam        启动LIO-SAM SLAM"
    echo "  status      查看系统状态"
    echo "  clean       清理Docker资源"
    echo
    echo "选项:"
    echo "  -h, --help  显示此帮助信息"
    echo "  -v          详细输出"
    echo
    echo "示例:"
    echo "  $0 build           # 构建镜像"
    echo "  $0 start           # 启动容器"
    echo "  $0 slam            # 运行SLAM系统"
    echo "  $0 rviz            # 启动可视化"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
}

# 检查网络连接
check_network() {
    print_info "检查与激光雷达的网络连接..."
    if ping -c 1 -W 3 *************** &> /dev/null; then
        print_success "激光雷达网络连接正常 (***************)"
        return 0
    else
        print_warning "无法连接到激光雷达 (***************)"
        print_info "请运行网络配置脚本: sudo ./scripts/setup_network.sh"
        return 1
    fi
}

# 构建Docker镜像
build_image() {
    print_info "构建LIO-SAM Docker镜像..."
    cd "$PROJECT_DIR"
    
    if docker-compose build; then
        print_success "Docker镜像构建完成"
    else
        print_error "Docker镜像构建失败"
        exit 1
    fi
}

# 启动容器
start_container() {
    print_info "启动LIO-SAM容器..."
    cd "$PROJECT_DIR"
    
    # 检查X11转发
    if [[ -n "$DISPLAY" ]]; then
        xhost +local:docker &> /dev/null || true
    fi
    
    if docker-compose up -d lio-sam; then
        print_success "LIO-SAM容器已启动"
        sleep 2
        show_status
    else
        print_error "容器启动失败"
        exit 1
    fi
}

# 停止容器
stop_container() {
    print_info "停止LIO-SAM容器..."
    cd "$PROJECT_DIR"
    
    if docker-compose down; then
        print_success "LIO-SAM容器已停止"
    else
        print_error "容器停止失败"
        exit 1
    fi
}

# 查看日志
show_logs() {
    print_info "显示容器日志..."
    cd "$PROJECT_DIR"
    docker-compose logs -f lio-sam
}

# 进入容器shell
enter_shell() {
    print_info "进入LIO-SAM容器..."
    if docker exec -it "$CONTAINER_NAME" /bin/bash; then
        print_success "已退出容器shell"
    else
        print_error "无法进入容器，请确保容器正在运行"
        exit 1
    fi
}

# 启动RViz
start_rviz() {
    print_info "启动RViz可视化..."
    cd "$PROJECT_DIR"
    
    if docker-compose --profile visualization up -d rviz; then
        print_success "RViz已启动"
    else
        print_error "RViz启动失败"
        exit 1
    fi
}

# 启动激光雷达驱动
start_lidar() {
    print_info "启动激光雷达驱动..."
    
    if ! docker exec "$CONTAINER_NAME" bash -c "
        source /opt/ros/noetic/setup.bash && 
        source /catkin_ws/devel/setup.bash && 
        roslaunch ws_30pcd_et3 scan_frame.launch
    "; then
        print_error "激光雷达驱动启动失败"
        exit 1
    fi
}

# 启动LIO-SAM SLAM
start_slam() {
    print_info "启动LIO-SAM SLAM系统..."
    
    if ! docker exec "$CONTAINER_NAME" bash -c "
        source /opt/ros/noetic/setup.bash && 
        source /catkin_ws/devel/setup.bash && 
        roslaunch lio_sam run_built_in_indoor.launch
    "; then
        print_error "LIO-SAM SLAM启动失败"
        exit 1
    fi
}

# 显示系统状态
show_status() {
    print_info "系统状态检查..."
    echo
    
    # Docker状态
    if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -q "$CONTAINER_NAME"; then
        print_success "容器状态: 运行中"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep "$CONTAINER_NAME"
    else
        print_warning "容器状态: 未运行"
    fi
    
    echo
    
    # 网络状态
    check_network
    
    echo
    
    # ROS状态（如果容器运行中）
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        print_info "ROS节点状态:"
        docker exec "$CONTAINER_NAME" bash -c "
            source /opt/ros/noetic/setup.bash && 
            source /catkin_ws/devel/setup.bash && 
            timeout 5 rosnode list 2>/dev/null || echo '  ROS Master未运行或无节点'
        " 2>/dev/null || print_warning "无法获取ROS状态"
    fi
}

# 清理Docker资源
clean_docker() {
    print_info "清理Docker资源..."
    cd "$PROJECT_DIR"
    
    # 停止并删除容器
    docker-compose down --volumes --remove-orphans
    
    # 删除镜像（可选）
    read -p "是否删除Docker镜像? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker rmi "$IMAGE_NAME" 2>/dev/null || true
        print_success "Docker资源已清理"
    else
        print_success "容器已停止，镜像保留"
    fi
}

# 主函数
main() {
    # 检查Docker
    check_docker
    
    # 解析命令行参数
    case "${1:-help}" in
        build)
            build_image
            ;;
        start)
            start_container
            ;;
        stop)
            stop_container
            ;;
        restart)
            stop_container
            sleep 2
            start_container
            ;;
        logs)
            show_logs
            ;;
        shell)
            enter_shell
            ;;
        rviz)
            start_rviz
            ;;
        lidar)
            start_lidar
            ;;
        slam)
            start_slam
            ;;
        status)
            show_status
            ;;
        clean)
            clean_docker
            ;;
        -h|--help|help)
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
