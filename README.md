# LIO-SAM Docker环境

这是一个为ROS1 Noetic LIO-SAM SLAM系统设计的Docker化开发环境，支持通过以太网连接激光雷达设备。

## 系统概述

本项目包含：
- **LIO-SAM**: 激光雷达惯性里程计SLAM系统
- **ws_30pcd_et3**: 激光雷达驱动包，支持IP地址为***************的激光雷达
- **Docker环境**: 完整的ROS1 Noetic运行环境
- **网络配置**: 自动化的网络设置脚本

## 快速开始

### 1. 系统要求

- Ubuntu 18.04+ 或其他支持Docker的Linux发行版
- Docker 20.10+
- Docker Compose 1.29+
- 至少4GB可用内存
- 网络接口可配置为***************

### 2. 网络配置

首先配置主机网络以连接激光雷达：

```bash
# 运行网络配置脚本（需要sudo权限）
sudo ./scripts/setup_network.sh
```

脚本会：
- 自动检测可用网络接口
- 配置静态IP地址（***************）
- 测试与激光雷达的连接
- 配置防火墙规则

### 3. 构建和启动

```bash
# 构建Docker镜像
./scripts/start_lio_sam.sh build

# 启动容器
./scripts/start_lio_sam.sh start

# 检查系统状态
./scripts/start_lio_sam.sh status
```

### 4. 运行LIO-SAM系统

```bash
# 方法1: 使用管理脚本（推荐）
./scripts/start_lio_sam.sh lidar    # 启动激光雷达驱动
./scripts/start_lio_sam.sh slam     # 启动LIO-SAM SLAM
./scripts/start_lio_sam.sh rviz     # 启动可视化

# 方法2: 手动进入容器
./scripts/start_lio_sam.sh shell
# 在容器内运行：
roslaunch ws_30pcd_et3 scan_frame.launch
roslaunch lio_sam run_built_in_indoor.launch
```

## 详细使用说明

### 管理脚本命令

```bash
./scripts/start_lio_sam.sh [命令]
```

可用命令：
- `build`: 构建Docker镜像
- `start`: 启动容器
- `stop`: 停止容器
- `restart`: 重启容器
- `logs`: 查看容器日志
- `shell`: 进入容器shell
- `rviz`: 启动RViz可视化
- `lidar`: 启动激光雷达驱动
- `slam`: 启动LIO-SAM SLAM
- `status`: 查看系统状态
- `clean`: 清理Docker资源

### Docker Compose使用

```bash
# 启动主服务
docker-compose up -d lio-sam

# 启动可视化服务
docker-compose --profile visualization up -d rviz

# 查看日志
docker-compose logs -f lio-sam

# 停止所有服务
docker-compose down
```

### 网络配置详情

激光雷达网络配置：
- **激光雷达IP**: ***************
- **主机IP**: ***************
- **子网**: *************/24
- **通信端口**: UDP（具体端口由激光雷达驱动确定）

Docker网络模式：
- 使用`host`网络模式，确保容器可以直接访问主机网络
- 支持激光雷达的UDP通信
- 允许RViz等GUI应用的X11转发

## 故障排除

### 常见问题

1. **无法连接激光雷达**
   ```bash
   # 检查网络连接
   ping ***************
   
   # 重新配置网络
   sudo ./scripts/setup_network.sh
   ```

2. **Docker构建失败**
   ```bash
   # 清理Docker缓存
   docker system prune -a
   
   # 重新构建
   ./scripts/start_lio_sam.sh build
   ```

3. **RViz无法显示**
   ```bash
   # 允许X11转发
   xhost +local:docker
   
   # 检查DISPLAY环境变量
   echo $DISPLAY
   ```

4. **容器内无法访问激光雷达**
   ```bash
   # 检查容器网络配置
   docker exec lio-sam-container ip addr show
   
   # 在容器内测试连接
   docker exec lio-sam-container ping ***************
   ```

### 日志查看

```bash
# 查看容器日志
./scripts/start_lio_sam.sh logs

# 查看ROS节点日志
docker exec lio-sam-container bash -c "
  source /opt/ros/noetic/setup.bash && 
  source /catkin_ws/devel/setup.bash && 
  rosnode list
"
```

## 开发模式

### 代码修改

源代码通过卷挂载到容器中，支持实时开发：

```bash
# 修改源代码后重新编译
docker exec lio-sam-container bash -c "
  cd /catkin_ws && 
  source /opt/ros/noetic/setup.bash && 
  catkin_make
"
```

### 调试模式

```bash
# 以调试模式启动容器
docker-compose run --rm lio-sam bash

# 在容器内手动启动各个组件
roscore &
roslaunch ws_30pcd_et3 scan_frame.launch &
roslaunch lio_sam run_built_in_indoor.launch
```

## 配置文件

### 激光雷达配置

主要配置文件：
- `src/ws_30pcd_et3_ros/launch/scan_frame.launch`: 激光雷达驱动配置
- `src/lio_sam/lio_sam/config/built_in_params_indoor.yaml`: LIO-SAM参数配置

### Docker配置

- `Dockerfile`: Docker镜像构建配置
- `docker-compose.yml`: 容器编排配置
- `.dockerignore`: Docker构建忽略文件

## 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   激光雷达设备    │    │   Docker容器     │    │   主机系统       │
│ *************** │◄──►│  ROS1 Noetic    │◄──►│  ROS2 Humble    │
│                 │    │  LIO-SAM        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 许可证

本项目遵循原始LIO-SAM和激光雷达驱动的许可证条款。

## 支持

如有问题，请检查：
1. 网络连接是否正常
2. Docker服务是否运行
3. 激光雷达设备是否正常工作
4. 查看容器日志获取详细错误信息
