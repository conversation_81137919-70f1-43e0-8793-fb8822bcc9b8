# ROS1 Noetic LIO-SAM Docker环境
# 基于Ubuntu 20.04和ROS Noetic构建，用于运行LIO-SAM SLAM系统
FROM ros:noetic-desktop-full

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV ROS_DISTRO=noetic
ENV CATKIN_WS=/catkin_ws

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    # 基础工具
    wget curl git vim nano \
    # 构建工具
    build-essential cmake \
    # ROS依赖
    python3-catkin-tools \
    python3-rosdep \
    python3-rosinstall \
    python3-rosinstall-generator \
    python3-wstool \
    # PCL和OpenCV依赖
    libpcl-dev \
    libopencv-dev \
    # GTSAM依赖
    libboost-all-dev \
    libeigen3-dev \
    # 网络工具
    net-tools \
    iputils-ping \
    iproute2 \
    # 其他依赖
    libtbb-dev \
    libgeographic-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装GTSAM (LIO-SAM的关键依赖)
RUN cd /tmp && \
    git clone https://github.com/borglab/gtsam.git && \
    cd gtsam && \
    git checkout 4.1.1 && \
    mkdir build && cd build && \
    cmake -DGTSAM_BUILD_WITH_MARCH_NATIVE=OFF \
          -DGTSAM_USE_SYSTEM_EIGEN=ON \
          -DGTSAM_BUILD_TESTS=OFF \
          -DGTSAM_BUILD_EXAMPLES_ALWAYS=OFF \
          -DCMAKE_BUILD_TYPE=Release .. && \
    make -j$(nproc) && \
    make install && \
    ldconfig && \
    cd / && rm -rf /tmp/gtsam

# 创建catkin工作空间
RUN mkdir -p $CATKIN_WS/src
WORKDIR $CATKIN_WS

# 复制源代码到容器
COPY src/ $CATKIN_WS/src/

# 初始化rosdep并安装ROS依赖
RUN rosdep init || true && \
    rosdep update && \
    rosdep install --from-paths src --ignore-src -r -y

# 构建工作空间
RUN /bin/bash -c "source /opt/ros/$ROS_DISTRO/setup.bash && \
    catkin_make -DCMAKE_BUILD_TYPE=Release"

# 设置ROS环境
RUN echo "source /opt/ros/$ROS_DISTRO/setup.bash" >> ~/.bashrc && \
    echo "source $CATKIN_WS/devel/setup.bash" >> ~/.bashrc

# 创建启动脚本
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# 源ROS环境\n\
source /opt/ros/$ROS_DISTRO/setup.bash\n\
source $CATKIN_WS/devel/setup.bash\n\
\n\
# 检查网络连接\n\
echo "检查与激光雷达的网络连接..."\n\
if ping -c 1 192.168.137.200 > /dev/null 2>&1; then\n\
    echo "✓ 激光雷达连接正常 (192.168.137.200)"\n\
else\n\
    echo "⚠ 警告: 无法连接到激光雷达 (192.168.137.200)"\n\
    echo "请确保:"\n\
    echo "1. 激光雷达已开机并连接到网络"\n\
    echo "2. Docker容器网络配置正确"\n\
    echo "3. 主机网络可以访问192.168.137.200"\n\
fi\n\
\n\
# 启动roscore如果没有运行\n\
if ! pgrep -x "rosmaster" > /dev/null; then\n\
    echo "启动ROS Master..."\n\
    roscore &\n\
    sleep 3\n\
fi\n\
\n\
# 执行传入的命令或启动bash\n\
exec "$@"' > /usr/local/bin/entrypoint.sh && \
    chmod +x /usr/local/bin/entrypoint.sh

# 设置工作目录
WORKDIR $CATKIN_WS

# 暴露ROS端口
EXPOSE 11311

# 设置入口点
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["/bin/bash"]
